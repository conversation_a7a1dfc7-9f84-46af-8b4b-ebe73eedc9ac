const namesList =
  "<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>," +
  "<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>," +
  "<PERSON>,<PERSON>,<PERSON>";
const names = namesList.split(",");

export function randomName(): string {
  const picked = names[Math.floor(Math.random() * names.length)];
  return Math.random() > 0.5 ? picked.slice(0, 3) : picked;
}
