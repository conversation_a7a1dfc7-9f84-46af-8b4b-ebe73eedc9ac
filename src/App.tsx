import { Layout } from "@/Layout";
import { SignInForm } from "@/SignInForm";
import { UserMenu } from "@/components/UserMenu";
import { Authenticated, Unauthenticated, useQuery } from "convex/react";
import { api } from "../convex/_generated/api";

export default function App() {
  const user = useQuery(api.users.viewer);
  return (
    <Layout
      menu={
        <Authenticated>
          <UserMenu>{user?.name ?? user?.email}</UserMenu>
        </Authenticated>
      }
    >
      <>
        <Authenticated>
          <div className="p-4">
            <h1 className="text-2xl font-bold">Welcome, {user?.name ?? user?.email ?? "User"}!</h1>
          </div>
        </Authenticated>
        <Unauthenticated>
          <div className="p-4">
            <h1 className="text-2xl font-bold">Welcome, anonymous!</h1>
            <SignInForm />
          </div>
        </Unauthenticated>
      </>
    </Layout>
  );
}
