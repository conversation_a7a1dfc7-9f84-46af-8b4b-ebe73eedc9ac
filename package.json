{"name": "cleaner-poker-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "npm-run-all --parallel dev:frontend dev:backend", "dev:frontend": "vite --open", "dev:backend": "convex dev", "predev": "convex dev --until-success && node setup.mjs --once && convex dashboard", "build": "tsc -b && vite build", "lint": "tsc && eslint .  --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@auth/core": "^0.37.4", "@convex-dev/auth": "^0.0.77", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toggle-group": "^1.1.11", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.26.1", "next-themes": "^0.3.0", "react": "^18.3.1", "react-dom": "^18.3.1", "sonner": "^2.0.7", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^20.19.11", "@types/react": "^18.3.24", "@types/react-dom": "^18.3.7", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitejs/plugin-react": "^4.7.0", "autoprefixer": "^10.4.21", "dotenv": "^16.6.1", "eslint": "^8.57.1", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.20", "npm-run-all": "^4.1.5", "postcss": "^8.5.6", "prettier": "3.3.2", "tailwindcss": "^3.4.17", "typescript": "^5.9.2", "vite": "^5.4.19"}}